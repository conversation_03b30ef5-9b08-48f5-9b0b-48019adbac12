# Environment Configuration
# Copy this file to .env and modify the values as needed

# Application Environment
# Options: development, testing, production
CI_ENV=development

# Base URL Configuration
# For local development, leave empty to auto-detect
# For production, set the full URL
BASE_URL=

# Database Configuration
DB_HOSTNAME=localhost
DB_USERNAME=root
DB_PASSWORD=
DB_DATABASE=your_database_name

# Security
COOKIE_SECURE=false
ENCRYPTION_KEY=v4ld01ntL@kuM@ju

# AWS Configuration (if needed)
# AWS_S3_KEY=
# AWS_S3_SECRET=
# MEDIA_BUCKET=
# MEDIA_CDN=
