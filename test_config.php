<?php
/**
 * Test Configuration Script
 * 
 * Script sederhana untuk test konfigurasi URL dan environment
 */

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value);
            if (!getenv($key)) {
                putenv("$key=$value");
                $_ENV[$key] = $value;
                $_SERVER[$key] = $value;
            }
        }
    }
}

// Simulate CodeIgniter config loading
if (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] != 'off') {
    $ht = "https://";
} else {
    $ht = "http://";
}

// Check if BASE_URL is set in environment, otherwise auto-detect
if (getenv('BASE_URL') && !empty(getenv('BASE_URL'))) {
    $base_url = getenv('BASE_URL');
} else {
    $root = $ht . $_SERVER['HTTP_HOST'];
    $root .= str_replace(basename($_SERVER['SCRIPT_NAME']), "", $_SERVER['SCRIPT_NAME']);
    $base_url = $root;
}

$cookie_secure = (getenv('COOKIE_SECURE') === 'true') ? true : false;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Konfigurasi AkuMaju</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
        .info { background: #f0f0f0; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Test Konfigurasi Web Lite AkuMaju</h1>
    
    <h2>Environment Variables</h2>
    <div class="info">
        <strong>CI_ENV:</strong> <?= getenv('CI_ENV') ?: 'tidak diset (default: development)' ?><br>
        <strong>BASE_URL:</strong> <?= getenv('BASE_URL') ?: 'tidak diset (auto-detect)' ?><br>
        <strong>COOKIE_SECURE:</strong> <?= getenv('COOKIE_SECURE') ?: 'tidak diset (default: false)' ?>
    </div>
    
    <h2>Konfigurasi yang Terdeteksi</h2>
    <div class="info">
        <strong>Base URL:</strong> <span class="<?= strpos($base_url, 'tokopandai.id') !== false ? 'error' : 'success' ?>"><?= $base_url ?></span><br>
        <strong>Protocol:</strong> <?= $ht ?><br>
        <strong>Cookie Secure:</strong> <span class="<?= $cookie_secure ? 'warning' : 'success' ?>"><?= $cookie_secure ? 'true' : 'false' ?></span><br>
        <strong>Server Name:</strong> <?= $_SERVER['HTTP_HOST'] ?><br>
        <strong>Script Name:</strong> <?= $_SERVER['SCRIPT_NAME'] ?>
    </div>
    
    <h2>Status</h2>
    <?php if (strpos($base_url, 'tokopandai.id') !== false): ?>
        <div class="error">
            ❌ <strong>MASALAH:</strong> Base URL masih mengarah ke production!<br>
            Pastikan file .env ada dan BASE_URL kosong untuk auto-detect.
        </div>
    <?php else: ?>
        <div class="success">
            ✅ <strong>BERHASIL:</strong> Base URL menggunakan server lokal.
        </div>
    <?php endif; ?>
    
    <?php if ($cookie_secure && $ht === 'http://'): ?>
        <div class="warning">
            ⚠️ <strong>PERINGATAN:</strong> Cookie secure diaktifkan tapi menggunakan HTTP.<br>
            Set COOKIE_SECURE=false di file .env untuk development lokal.
        </div>
    <?php else: ?>
        <div class="success">
            ✅ <strong>BERHASIL:</strong> Konfigurasi cookie sesuai dengan protocol.
        </div>
    <?php endif; ?>
    
    <h2>File Environment</h2>
    <div class="info">
        <strong>.env file:</strong> <?= file_exists(__DIR__ . '/.env') ? '✅ Ada' : '❌ Tidak ada' ?><br>
        <strong>.htaccess file:</strong> <?= file_exists(__DIR__ . '/.htaccess') ? '✅ Ada' : '❌ Tidak ada' ?>
    </div>
    
    <h2>Langkah Selanjutnya</h2>
    <ol>
        <li>Jika semua status menunjukkan ✅, hapus file ini dan coba login ke aplikasi</li>
        <li>Jika ada ❌ atau ⚠️, ikuti petunjuk di file LOCAL_SETUP.md</li>
        <li>Clear browser cache dan cookies sebelum test login</li>
        <li>Pastikan menggunakan URL: <strong><?= $base_url ?></strong></li>
    </ol>
    
    <p><small>File ini hanya untuk testing. Hapus setelah konfigurasi selesai.</small></p>
</body>
</html>
