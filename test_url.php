<?php

/**
 * Test URL Configuration
 *
 * Script sederhana untuk test konfigurasi URL tanpa environment variables
 */

// Simulate CodeIgniter config loading
if (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] != 'off') {
    $ht = "https://";
} else {
    $ht = "http://";
}

// Auto-detect base URL
$root = $ht . $_SERVER['HTTP_HOST'];
$root .= str_replace(basename($_SERVER['SCRIPT_NAME']), "", $_SERVER['SCRIPT_NAME']);
$base_url = $root;

?>
<!DOCTYPE html>
<html>

<head>
    <title>Test URL AkuMaju</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
        }

        .success {
            color: green;
        }

        .error {
            color: red;
        }

        .info {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>

<body>
    <h1>Test URL Configuration - Web Lite AkuMaju</h1>

    <h2>Konfigurasi yang Terdeteksi</h2>
    <div class="info">
        <strong>Base URL:</strong> <span class="<?= strpos($base_url, 'tokopandai.id') !== false ? 'error' : 'success' ?>"><?= $base_url ?></span><br>
        <strong>Protocol:</strong> <?= $ht ?><br>
        <strong>Server Name:</strong> <?= $_SERVER['HTTP_HOST'] ?><br>
        <strong>Script Path:</strong> <?= dirname($_SERVER['SCRIPT_NAME']) ?>
    </div>

    <h2>Status</h2>
    <?php if (strpos($base_url, 'tokopandai.id') !== false): ?>
        <div class="error">
            ❌ <strong>MASALAH:</strong> Base URL masih mengarah ke production!<br>
            Periksa konfigurasi di application/config/config.php
        </div>
    <?php else: ?>
        <div class="success">
            ✅ <strong>BERHASIL:</strong> Base URL menggunakan server lokal.
        </div>
    <?php endif; ?>

    <h2>File Konfigurasi</h2>
    <div class="info">
        <strong>.htaccess:</strong> <?= file_exists(__DIR__ . '/.htaccess') ? '✅ Ada' : '❌ Tidak ada' ?><br>
        <strong>config.php:</strong> <?= file_exists(__DIR__ . '/application/config/config.php') ? '✅ Ada' : '❌ Tidak ada' ?><br>
        <strong>config.php.example:</strong> <?= file_exists(__DIR__ . '/application/config/config.php.example') ? '✅ Ada' : '❌ Tidak ada' ?><br>
        <strong>.gitignore:</strong> <?= file_exists(__DIR__ . '/.gitignore') ? '✅ Ada' : '❌ Tidak ada' ?>
    </div>

    <h2>Langkah Selanjutnya</h2>
    <ol>
        <li>Jika status menunjukkan ✅, hapus file ini dan coba login ke aplikasi</li>
        <li>Clear browser cache dan cookies sebelum test login</li>
        <li>Gunakan URL: <strong><?= $base_url ?></strong></li>
        <li>Untuk production, ubah konfigurasi di application/config/config.php</li>
    </ol>

    <p><small>File ini hanya untuk testing. Hapus setelah konfigurasi selesai.</small></p>
</body>

</html>