# Setup Local Development - Web Lite AkuMaju

## Quick Start

1. **Copy konfigurasi:**
   ```bash
   cp application/config/config.php.example application/config/config.php
   ```

2. **Test konfigurasi:**
   Akses: `http://localhost/web-lite-akumaju/test_url.php`

3. **Jika status ✅, hapus file test:**
   ```bash
   rm test_url.php
   ```

4. **Akses aplikasi:**
   `http://localhost/web-lite-akumaju/`

## Konfigurasi Auto-Detection

File `config.php` sudah dikonfigurasi untuk auto-detect URL:

```php
// Auto-detect base URL for local development
$root = $ht . $_SERVER['HTTP_HOST'];
$root .= str_replace(basename($_SERVER['SCRIPT_NAME']), "", $_SERVER['SCRIPT_NAME']);
$config['base_url'] = $root;
```

## Git Ignore

File `config.php` sudah ditambahkan ke `.gitignore` sehingga:
- ✅ Tidak akan ter-commit ke repository
- ✅ Setiap developer bisa punya konfigurasi sendiri
- ✅ Tidak ada konflik konfigurasi antar developer

## Production Deployment

Untuk production, ubah di `config.php`:

```php
// Ganti dengan URL production
$config['base_url'] = 'https://lite.tokopandai.id/ak-mj/';
$config['cookie_secure'] = TRUE;
```

## Troubleshooting

- **Masih redirect ke production?** Clear browser cache & cookies
- **Session tidak bekerja?** Pastikan `cookie_secure = FALSE`
- **URL tidak terdeteksi?** Restart web server
