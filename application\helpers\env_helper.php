<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Environment Helper
 * 
 * Helper functions for loading and managing environment variables
 */

if (!function_exists('load_env')) {
    /**
     * Load environment variables from .env file
     * 
     * @param string $file_path Path to .env file
     * @return bool True if file was loaded successfully
     */
    function load_env($file_path = null) {
        if ($file_path === null) {
            $file_path = FCPATH . '.env';
        }
        
        if (!file_exists($file_path)) {
            return false;
        }
        
        $lines = file($file_path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            // <PERSON><PERSON> comments
            if (strpos(trim($line), '#') === 0) {
                continue;
            }
            
            // Parse key=value pairs
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);
                
                // Remove quotes if present
                if (preg_match('/^"(.*)"$/', $value, $matches)) {
                    $value = $matches[1];
                } elseif (preg_match("/^'(.*)'$/", $value, $matches)) {
                    $value = $matches[1];
                }
                
                // Set environment variable
                if (!getenv($key)) {
                    putenv("$key=$value");
                    $_ENV[$key] = $value;
                    $_SERVER[$key] = $value;
                }
            }
        }
        
        return true;
    }
}

if (!function_exists('env')) {
    /**
     * Get environment variable with optional default value
     * 
     * @param string $key Environment variable key
     * @param mixed $default Default value if key not found
     * @return mixed Environment variable value or default
     */
    function env($key, $default = null) {
        $value = getenv($key);
        
        if ($value === false) {
            return $default;
        }
        
        // Convert string representations of boolean values
        switch (strtolower($value)) {
            case 'true':
            case '(true)':
                return true;
            case 'false':
            case '(false)':
                return false;
            case 'empty':
            case '(empty)':
                return '';
            case 'null':
            case '(null)':
                return null;
        }
        
        return $value;
    }
}
