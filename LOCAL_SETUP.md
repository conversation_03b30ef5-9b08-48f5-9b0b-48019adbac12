# Setup Lokal untuk Web Lite AkuMaju

## <PERSON>bahan yang Dilakukan

Untuk mengatasi masalah URL yang mengarah ke production (`https://lite.tokopandai.id/ak-mj/`) saat development lokal, beberapa perubahan telah dilakukan:

### 1. Konfigurasi Base URL Auto-Detection

File `application/config/config.php` telah dimodifikasi untuk:
- Auto-detect URL berdasarkan server lokal
- Mendukung environment variables untuk konfigurasi yang fleksibel
- Menggunakan HTTP untuk development lokal

### 2. Konfigurasi Cookie Security

- `cookie_secure` diset ke `FALSE` untuk development lokal
- Mendukung environment variable `COOKIE_SECURE` untuk kontrol yang lebih baik

### 3. File Environment

- `.env` - File konfigurasi untuk development lokal
- `.env.example` - Template konfigurasi
- Environment variables dimuat otomatis saat aplikasi start

### 4. URL Rewriting

- File `.htaccess` ditambahkan untuk URL rewriting yang proper
- Redirect HTTPS dinonaktifkan untuk development lokal

## Cara Penggunaan

### Development Lokal

1. Pastikan file `.env` ada dengan konfigurasi:
   ```
   CI_ENV=development
   BASE_URL=
   COOKIE_SECURE=false
   ```

2. Akses aplikasi melalui URL lokal (contoh: `http://localhost/web-lite-akumaju`)

3. URL akan otomatis terdeteksi berdasarkan server lokal

### Production

1. Buat file `.env` dengan konfigurasi production:
   ```
   CI_ENV=production
   BASE_URL=https://lite.tokopandai.id/ak-mj/
   COOKIE_SECURE=true
   ```

2. Deploy aplikasi ke server production

## File yang Dimodifikasi

1. `application/config/config.php` - Konfigurasi base URL dan cookie
2. `index.php` - Loading environment variables
3. `.htaccess` - URL rewriting rules
4. `.gitignore` - Mengabaikan file environment
5. `application/helpers/env_helper.php` - Helper untuk environment variables

## Troubleshooting

### Jika masih redirect ke production:

1. Clear browser cache dan cookies
2. Pastikan file `.env` ada dan berisi konfigurasi yang benar
3. Restart web server (Apache/Nginx)
4. Periksa file `.htaccess` tidak ada redirect hardcode

### Jika session tidak bekerja:

1. Pastikan `COOKIE_SECURE=false` di file `.env`
2. Periksa permission folder `application/cache` dan `application/logs`
3. Pastikan menggunakan HTTP (bukan HTTPS) untuk development lokal

## Keamanan

- File `.env` sudah ditambahkan ke `.gitignore` untuk mencegah commit ke repository
- Konfigurasi production dan development terpisah
- Cookie security otomatis disesuaikan berdasarkan environment
