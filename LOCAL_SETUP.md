# Setup Lokal untuk Web Lite AkuMaju

## Perubahan yang Dilakukan

Untuk mengatasi masalah URL yang mengarah ke production (`https://lite.tokopandai.id/ak-mj/`) saat development lokal, beberapa perubahan telah dilakukan:

### 1. Konfigurasi Base URL Auto-Detection

File `application/config/config.php` telah dimodifikasi untuk:
- Auto-detect URL berdasarkan server lokal
- Menghapus hardcode URL production
- Menggunakan HTTP/HTTPS sesuai dengan server

### 2. Konfigurasi Cookie Security

- `cookie_secure` diset ke `FALSE` untuk development lokal
- Memungkinkan session bekerja dengan HTTP

### 3. URL Rewriting

- File `.htaccess` ditambahkan untuk URL rewriting yang proper
- Redirect HTTPS dinonaktifkan untuk development lokal

## Cara Penggunaan

### Setup Awal untuk Developer Baru

1. Copy file template: `cp application/config/config.php.example application/config/config.php`
2. File `config.php` sudah dikonfigurasi untuk auto-detect URL lokal
3. File `config.php` tidak akan ter-commit ke git (sudah ada di .gitignore)

### Development Lokal

1. Akses aplikasi melalui URL lokal (contoh: `http://localhost/web-lite-akumaju`)
2. URL akan otomatis terdeteksi berdasarkan server lokal
3. Session dan cookie akan bekerja dengan HTTP

### Production

Untuk production, ubah konfigurasi di `application/config/config.php`:

```php
// Untuk production, ganti auto-detect dengan URL tetap
$config['base_url'] = 'https://lite.tokopandai.id/ak-mj/';

// Dan aktifkan cookie secure
$config['cookie_secure'] = TRUE;
```

## File yang Dimodifikasi

1. `application/config/config.php` - Konfigurasi base URL dan cookie (ditambahkan ke .gitignore)
2. `application/config/config.php.example` - Template konfigurasi untuk developer lain
3. `.htaccess` - URL rewriting rules
4. `.gitignore` - Menambahkan config.php agar tidak ter-commit

## Troubleshooting

### Jika masih redirect ke production:

1. Clear browser cache dan cookies
2. Restart web server (Apache/Nginx)
3. Periksa tidak ada redirect hardcode di controller atau view
4. Pastikan file `.htaccess` tidak ada redirect paksa

### Jika session tidak bekerja:

1. Pastikan `cookie_secure = FALSE` di `application/config/config.php`
2. Periksa permission folder `application/cache` dan `application/logs`
3. Pastikan menggunakan HTTP untuk development lokal
4. Clear browser cookies

## Switching ke Production

Saat deploy ke production:

1. Ubah `$config['base_url']` ke URL production
2. Ubah `$config['cookie_secure']` ke `TRUE`
3. Pastikan menggunakan HTTPS di production

## Keamanan

- Cookie security disesuaikan dengan environment
- Auto-detection hanya untuk development
- Production menggunakan URL tetap dan HTTPS
